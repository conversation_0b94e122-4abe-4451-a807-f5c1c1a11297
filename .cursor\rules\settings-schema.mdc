---
description: Guidelines and examples for organizing and structuring the Shopify theme settings schema.
globs: config/settings_schema.json
alwaysApply: false
---
# Settings schema standards

## Settings schema structure

```json
{
  "name": "theme_info",
  "theme_name": "Theme Name",
  "theme_version": "1.0.0",
  "theme_author": "Author Name",
  "theme_documentation_url": "https://...",
  "theme_support_url": "https://..."
},
{
  "name": "Colors",
  "settings": [
    {
      "type": "header",
      "content": "Brand Colors"
    },
    {
      "type": "color",
      "id": "color_primary",
      "label": "Primary",
      "default": "#121212"
    }
  ]
}
```

## Setting categories

**Typography:**
- `font_picker` for font selections
- `range` for font sizes (12-72px)
- `select` for font weights

**Layout:**
- `range` for spacing (0-100px)
- `select` for layout options
- `checkbox` for feature toggles

**Performance:**
- `checkbox` for lazy loading
- `select` for image quality
- `number` for pagination limits
