{% comment %}
  This section is used in the password template to render landing page shown
  when password protection is applied to a store.

  https://shopify.dev/docs/storefronts/themes/architecture/templates/password
{% endcomment %}

<h1>{{ 'password.title' | t }}</h1>

{% if shop.password_message %}
  <p>{{ shop.password_message }}</p>
{% endif %}

{% form 'storefront_password' %}
  {% if form.errors %}
    {{ form.errors | default_errors }}
  {% endif %}

  <label for="password-input">
    {{ 'password.password' | t }}
  </label>

  <input type="password" name="password" id="password-input">

  <button type="submit">
    {{ 'password.enter' | t }}
  </button>
{% endform %}

{% schema %}
{
  "name": "t:general.password",
  "settings": []
}
{% endschema %}
