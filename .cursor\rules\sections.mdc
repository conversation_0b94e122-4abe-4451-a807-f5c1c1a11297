---
description: Section coding standards and best practices guide
globs: sections/*.liquid
alwaysApply: false
---
# Section development standards

## Section requirements

Every section must include:

- `{% schema %}` tag with valid JSON
- Proper HTML semantic structure
- CSS scoping with section classes
- Translation keys for all text

## Section patterns

**Basic section structure:**

```liquid
{% liquid
  assign section_id = section.settings.custom_id | default: section.id
  assign section_class = 'section-' | append: section.type
%}

<section
  id="{{ section_id }}"
  class="{{ section_class }}"
  style="
    --section-padding-top: {{ section.settings.padding_top }}px;
    --section-padding-bottom: {{ section.settings.padding_bottom }}px;
  "
>
  <div class="page-width">
    {% content_for 'blocks %}
  </div>
</section>

{% stylesheet %}
.{{ section_class }} {
  padding-top: var(--section-padding-top, 40px);
  padding-bottom: var(--section-padding-bottom, 40px);
}
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.section_name",
  "tag": "section",
  "class": "section-name",
  "blocks": [
    {"type": "@theme"},
    {"type": "@app"}
  ],
  "settings": [
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:settings.padding",
      "min": 0,
      "max": 100,
      "default": 40,
      "unit": "px"
    }
  ],
  "presets": [
    {
      "name": "t:names.section_name"
    }
  ]
}
{% endschema %}
```

## Performance patterns

- Use `{% liquid %}` for multiline logic
- Lazy load images with `loading="lazy"`
- Scope CSS variables to section
- Use `container-queries` for responsive behavior

[section-example.liquid](mdc:.cursor/rules/examples/section-example.liquid)

