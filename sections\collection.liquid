{% comment %}
  This section is used in the collection template to render collection page
  listing all products within a collection.

  https://shopify.dev/docs/storefronts/themes/architecture/templates/collection
{% endcomment %}

<h1>{{ collection.title }}</h1>

<div class="collection-products">
  {% paginate collection.products by 20 %}
    {% for product in collection.products %}
      <div class="collection-product">
        {% if product.featured_image %}
          {% render 'image',
            class: 'collection-product__image',
            image: product.featured_image,
            url: product.url,
            width: 400,
            height: 400,
            crop: 'center'
          %}
        {% endif %}
        <div class="collection-product__content">
          <p>{{ product.title | escape | link_to: product.url }}</p>
          <p>{{ product.price | money }}</p>
        </div>
      </div>
    {% endfor %}

    {{ paginate | default_pagination }}
  {% endpaginate %}
</div>

{% stylesheet %}
  .collection-products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:general.collection",
  "settings": []
}
{% endschema %}
