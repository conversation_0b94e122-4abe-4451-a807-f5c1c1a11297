---
description: JSON schemas to write config files, translations, and the {% schema %} tag in blocks and sections.
globs: blocks/*.liquid, sections/*.liquid, config/settings_schema.json, locales/*.json
alwaysApply: false
---

# JSON schemas for Liquid themes

## The `{% schema %}` tag on blocks and sections

**Key principles: follow the "Good practices" and "Validate the `{% schema %}` content" using JSON schemas**

### Good practices

When defining the `{% schema %}` tag on sections and blocks, follow these guidelines to use the values:

**Single property settings**: For settings that correspond to a single CSS property, use CSS variables:
```liquid
<div class="collection" style="--gap: {{ block.settings.gap }}px">
  Example
</div>

{% stylesheet %}
  .collection {
    gap: var(--gap);
  }
{% endstylesheet %}

{% schema %}
{
  "settings": [{
    "type": "range",
    "label": "gap",
    "id": "gap",
    "min": 0,
    "max": 100,
    "unit": "px",
    "default": 0,
  }]
}
{% endschema %}
```

**Multiple property settings**: For settings that control multiple CSS properties, use CSS classes:
```liquid
<div class="collection {{ block.settings.layout }}">
  Example
</div>

{% stylesheet %}
  .collection--full-width {
    /* multiple styles */
  }
  .collection--narrow {
    /* multiple styles */
  }
{% endstylesheet %}

{% schema %}
{
  "settings": [{
    "type": "select",
    "id": "layout",
    "label": "layout",
    "values": [
      { "value": "collection--full-width", "label": "t:options.full" },
      { "value": "collection--narrow", "label": "t:options.narrow" }
    ]
  }]
}
{% endschema %}
```

#### Mobile layouts

If you need to create a mobile layout and you want the merchant to be able to select one or two columns, use a select input:

```liquid
{% schema %}
{
  "type": "select",
  "id": "columns_mobile",
  "label": "Columns on mobile",
  "options": [
    { "value": 1, "label": "1" },
    { "value": "2", "label": "2" }
  ]
}
{% endschema %}
```

### Validate the `{% schema %}` content

Use this mafinest to validate schemas to validate

```
{
  "$schema": "manifest_schema.json",
  "$comment": "Declares all the JSON schemas you need to validate themes",
  "schemas": [
    { "fileMatch": ["locales/*.json"], "uri": "schemas/translations.json" },
    { "fileMatch": ["blocks/*.liquid"], "uri": "schemas/theme_block.json" },
    { "fileMatch": ["config/settings_schema.json"], "uri": "schemas/theme_settings.json" },
    { "fileMatch": ["sections/*.liquid"], "uri": "schemas/section.json" },
    { "uri": "schemas/settings.json" },
    { "uri": "schemas/setting.json" },
    { "uri": "schemas/default_setting_values.json" },
    { "uri": "schemas/app_block_entry.json" },
    { "uri": "schemas/theme_block_entry.json" },
    { "uri": "schemas/targetted_block_entry.json" },
    { "uri": "schemas/preset_blocks.json" },
    { "uri": "schemas/preset.json" },
    { "uri": "schemas/local_block_entry.json" }
  ]
}
```


**schemas/app_block_entry.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","$comment":"An @app block entry","type":"object","additionalProperties":false,"properties":{"type":{"const":"@app","description":"The \"@app\" type is used to denote that this container accepts app blocks. App blocks enable app developers to create blocks for merchants to add app content to their theme without having to directly edit theme code.","markdownDescription":"The `@app` type is used to denote that this container accepts app blocks. [App blocks](https://shopify.dev/docs/themes/architecture/sections/app-blocks) enable app developers to create blocks for merchants to add app content to their theme without having to directly edit theme code.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/app-blocks#supporting-app-blocks)"}}}
```

**schemas/default_setting_values.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","type":"object","description":"A list of default values for any settings that you might want to populate. Each entry should include the setting name and the value.","additionalProperties":{"anyOf":[{"type":"number"},{"type":"boolean"},{"type":"string"},{"type":"array","items":{"type":"string"}}]}}
```

**schemas/local_block_entry.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","$comment":"For block definitions that are local to the file.","type":"object","required":["type","name"],"additionalProperties":false,"properties":{"type":{"type":"string","description":"The block type. This is a free-form string that you can use as an identifier.","markdownDescription":"The block type. This is a free-form string that you can use as an identifier.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#blocks)"},"name":{"type":"string","description":"The block name, which will show as the block title in the theme editor."},"limit":{"type":"integer","description":"The number of blocks of this type that can be used."},"settings":{"$ref":"./settings.json"}}}
```

**schemas/preset_blocks.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","title":"Shopify Liquid Preset Blocks Schema","definitions":{"blocksArray":{"type":"array","description":"A list of child blocks that you might want to include.","markdownDescription":"A list of child blocks that you might want to include.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#presets)","items":{"type":"object","allOf":[{"$ref":"#/definitions/commonBlockAttributes"}],"properties":{"static":true,"type":true,"name":true,"settings":true,"blocks":{"$ref":"#/definitions/blocksArray"},"id":{"type":"string","description":"A unique identifier for the block."}},"additionalProperties":false,"if":{"properties":{"static":{"const":true}},"required":["static"]},"then":{"required":["id"]}}},"blocksHash":{"type":"object","description":"A list of child blocks that you might want to include.","markdownDescription":"A list of child blocks that you might want to include.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#presets)","additionalProperties":{"allOf":[{"$ref":"#/definitions/commonBlockAttributes"}],"properties":{"static":true,"type":true,"name":true,"settings":true,"blocks":{"$ref":"#/definitions/blocksHash"},"block_order":{"type":"array","description":"The order of the blocks in the section."}},"additionalProperties":false}},"commonBlockAttributes":{"type":"object","required":["type"],"properties":{"type":{"type":"string","description":"The block type."},"name":{"type":"string","description":"The block name."},"settings":{"$ref":"./default_setting_values.json"},"static":{"type":"boolean","description":"If the block is rendered statically or not."}}}}}
```

**schemas/preset.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","title":"Shopify Liquid Section or Block Preset Schema","oneOf":[{"$ref":"#/definitions/presetWithBlocksArray"},{"$ref":"#/definitions/presetWithBlocksHash"}],"definitions":{"presetBase":{"type":"object","required":["name"],"properties":{"name":{"type":"string","description":"The preset name, which will show in the 'Add section' or 'Add block' picker of the theme editor.","markdownDescription":"The preset name, which will show in the 'Add section' or 'Add block' picker of the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#presets)"},"category":{"type":"string","description":"The category of the preset, which will show in the 'Add section' or 'Add block' picker of the theme editor.","markdownDescription":"The category of the preset, which will show in the 'Add section' or 'Add block' picker of the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#presets)"},"settings":{"$ref":"./default_setting_values.json"}}},"presetWithBlocksArray":{"type":"object","allOf":[{"$ref":"#/definitions/presetBase"}],"properties":{"name":true,"category":true,"settings":true,"blocks":{"$ref":"./preset_blocks.json#/definitions/blocksArray"}},"additionalProperties":false},"presetWithBlocksHash":{"type":"object","allOf":[{"$ref":"#/definitions/presetBase"}],"required":["blocks"],"properties":{"name":true,"settings":true,"blocks":{"$ref":"./preset_blocks.json#/definitions/blocksHash"},"block_order":{"type":"array","description":"The order of blocks in the preset.","markdownDescription":"The order of blocks in the preset."},"additionalProperties":false}}}}
```

**schemas/section.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","title":"Shopify Liquid Theme Section Schema","type":"object","additionalProperties":false,"properties":{"name":{"type":"string","description":"The name attribute determines the section title that is shown in the theme editor.","markdownDescription":"The `name` attribute determines the section title that is shown in the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#name)"},"tag":{"type":"string","description":"The HTML element that is used to wrap the section.","markdownDescription":"The HTML element that is used to wrap the section.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#tag)","enum":["article","aside","div","footer","header","section"]},"class":{"type":"string","description":"Additional CSS class for the section.","markdownDescription":"Additional CSS class for the section.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#class)"},"limit":{"type":"integer","description":"The number of times a section can be added to a template or section group.","markdownDescription":"The number of times a section can be added to a template or section group.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#limit)","minimum":1,"maximum":2},"settings":{"$ref":"./settings.json"},"max_blocks":{"type":"integer","description":"There's a limit of 50 blocks per section. You can specify a lower limit with the max_blocks attribute","markdownDescription":"There's a limit of 50 blocks per section. You can specify a lower limit with the `max_blocks` attribute.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#max_blocks)","minimum":1,"maximum":50},"blocks":{"type":"array","description":"Blocks are reusable modules of content that can be added, removed, and reordered within a section.","markdownDescription":"Blocks are reusable modules of content that can be added, removed, and reordered within a section.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#blocks)","properties":{"type":{"description":"The block type. Can be one of the following values: @theme, @app, or a custom block type.","markdownDescription":"The block type. Can be one of the following values: @theme, @app, or a custom block type.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#blocks)"}},"items":{"type":"object","required":["type"],"properties":{"type":{"type":"string","description":"The type of block that can be added to this block.","markdownDescription":"The type of block that can be added to this block.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#blocks)"}},"$comment":"The allOf rule here exists because that's how we do discriminated unions in JSON schemas. If a rule matches, that rule will be used to document the type property. Otherwise we fallback to the docs above.","allOf":[{"if":{"required":["type"],"properties":{"type":{"const":"@theme"}}},"then":{"$ref":"./theme_block_entry.json"}},{"if":{"required":["type"],"properties":{"type":{"const":"@app"}}},"then":{"$ref":"./app_block_entry.json"}},{"if":{"required":["type"],"properties":{"type":{"type":"string","not":{"enum":["@app","@theme"]}}}},"then":{"oneOf":[{"$ref":"./targetted_block_entry.json"},{"$ref":"./local_block_entry.json"}]}}]}},"presets":{"type":"array","description":"Presets are default configurations of sections that enable users to easily add a section to a JSON template through the theme editor.","markdownDescription":"Presets are default configurations of sections that enable users to easily add a section to a [JSON template](https://shopify.dev/docs/themes/architecture/templates/json-templates) through the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#presets)","items":{"$ref":"./preset.json"}},"default":{"type":"object","description":"Default configuration for statically rendered sections.","markdownDescription":"Default configuration for statically rendered sections.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#default)","properties":{"settings":{"$ref":"./default_setting_values.json"},"blocks":{"type":"array","description":"Default blocks configurations to ship with this default.","markdownDescription":"Default blocks configurations to ship with this default.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#default)","items":{"type":"object","required":["type"],"properties":{"type":{"type":"string","description":"The block type."},"settings":{"$ref":"./default_setting_values.json"}}}}}},"locales":{"type":"object","description":"Sections can provide their own set of translated strings through the locales object. This is separate from the locales directory of the theme, which makes it a useful feature for sections that are meant to be installed on multiple themes or shops.","markdownDescription":"Sections can provide their own set of translated strings through the `locales` object. This is separate from the `locales` directory of the theme, which makes it a useful feature for sections that are meant to be installed on multiple themes or shops.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#locales)","additionalProperties":{"type":"object","additionalProperties":{"type":"string"}}},"enabled_on":{"description":"Restrict the section to certain template page types and section group types.","markdownDescription":"Restrict the section to certain template page types and section group types.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#enabled_on)","$ref":"#/definitions/sectionToggle"},"disabled_on":{"description":"Prevent the section from being used on certain template page types and section group types.","markdownDescription":"Prevent the section from being used on certain template page types and section group types.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/sections/section-schema#disabled_on)","$ref":"#/definitions/sectionToggle"}},"definitions":{"sectionToggle":{"type":"object","description":"Restrict the section to certain template page types and section group types.","additionalProperties":false,"properties":{"templates":{"type":"array","items":{"type":"string","enum":["*","404","article","blog","captcha","cart","collection","customers/account","customers/activate_account","customers/addresses","customers/login","customers/order","customers/register","customers/reset_password","gift_card","index","list-collections","metaobject","page","password","policy","product","search"]},"uniqueItems":true},"groups":{"type":"array","items":{"type":"string"},"uniqueItems":true}}}}}
```

**schemas/setting.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","type":"object","properties":{"type":{"description":"The type of the input or sidebar setting. This value determines the type of field that gets rendered into the editor.","markdownDescription":"The type of the input or sidebar setting. This value determines the type of field that gets rendered into the editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings)","enum":["article","blog","checkbox","collection","collection_list","color","color_background","color_scheme","color_scheme_group","font_picker","header","html","image_picker","inline_richtext","link_list","liquid","metaobject","metaobject_list","number","page","paragraph","product","product_list","radio","range","richtext","select","style.layout_panel","style.size_panel","style.spacing_panel","text","text_alignment","textarea","url","video","video_url"]}},"$comment":"We're doing the weird allOf here so that we have better error messages when type is invalid. We're also doing the allOf so that we can use additionalProperties properly.","allOf":[{"if":{"required":["type"],"properties":{"type":{"const":"header"}}},"then":{"$ref":"#/definitions/header"}},{"if":{"required":["type"],"properties":{"type":{"const":"paragraph"}}},"then":{"$ref":"#/definitions/paragraph"}},{"if":{"required":["type"],"properties":{"type":{"const":"article"}}},"then":{"$ref":"#/definitions/article"}},{"if":{"required":["type"],"properties":{"type":{"const":"blog"}}},"then":{"$ref":"#/definitions/blog"}},{"if":{"required":["type"],"properties":{"type":{"const":"checkbox"}}},"then":{"$ref":"#/definitions/checkbox"}},{"if":{"required":["type"],"properties":{"type":{"const":"collection"}}},"then":{"$ref":"#/definitions/collection"}},{"if":{"required":["type"],"properties":{"type":{"const":"collection_list"}}},"then":{"$ref":"#/definitions/collection_list"}},{"if":{"required":["type"],"properties":{"type":{"const":"color"}}},"then":{"$ref":"#/definitions/color"}},{"if":{"required":["type"],"properties":{"type":{"const":"color_background"}}},"then":{"$ref":"#/definitions/color_background"}},{"if":{"required":["type"],"properties":{"type":{"const":"color_scheme"}}},"then":{"$ref":"#/definitions/color_scheme"}},{"if":{"required":["type"],"properties":{"type":{"const":"color_scheme_group"}}},"then":{"$ref":"#/definitions/color_scheme_group"}},{"if":{"required":["type"],"properties":{"type":{"const":"font_picker"}}},"then":{"$ref":"#/definitions/font_picker"}},{"if":{"required":["type"],"properties":{"type":{"const":"html"}}},"then":{"$ref":"#/definitions/html"}},{"if":{"required":["type"],"properties":{"type":{"const":"image_picker"}}},"then":{"$ref":"#/definitions/image_picker"}},{"if":{"required":["type"],"properties":{"type":{"const":"inline_richtext"}}},"then":{"$ref":"#/definitions/inline_richtext"}},{"if":{"required":["type"],"properties":{"type":{"const":"link_list"}}},"then":{"$ref":"#/definitions/link_list"}},{"if":{"required":["type"],"properties":{"type":{"const":"liquid"}}},"then":{"$ref":"#/definitions/liquid"}},{"if":{"required":["type"],"properties":{"type":{"const":"metaobject"}}},"then":{"$ref":"#/definitions/metaobject"}},{"if":{"required":["type"],"properties":{"type":{"const":"metaobject_list"}}},"then":{"$ref":"#/definitions/metaobject_list"}},{"if":{"required":["type"],"properties":{"type":{"const":"number"}}},"then":{"$ref":"#/definitions/number"}},{"if":{"required":["type"],"properties":{"type":{"const":"page"}}},"then":{"$ref":"#/definitions/page"}},{"if":{"required":["type"],"properties":{"type":{"const":"product"}}},"then":{"$ref":"#/definitions/product"}},{"if":{"required":["type"],"properties":{"type":{"const":"product_list"}}},"then":{"$ref":"#/definitions/product_list"}},{"if":{"required":["type"],"properties":{"type":{"const":"radio"}}},"then":{"$ref":"#/definitions/radio"}},{"if":{"required":["type"],"properties":{"type":{"const":"range"}}},"then":{"$ref":"#/definitions/range"}},{"if":{"required":["type"],"properties":{"type":{"const":"richtext"}}},"then":{"$ref":"#/definitions/richtext"}},{"if":{"required":["type"],"properties":{"type":{"const":"select"}}},"then":{"$ref":"#/definitions/select"}},{"if":{"required":["type"],"properties":{"type":{"const":"style.layout_panel"}}},"then":{"$ref":"#/definitions/style.layout_panel"}},{"if":{"required":["type"],"properties":{"type":{"const":"style.size_panel"}}},"then":{"$ref":"#/definitions/style.size_panel"}},{"if":{"required":["type"],"properties":{"type":{"const":"style.spacing_panel"}}},"then":{"$ref":"#/definitions/style.spacing_panel"}},{"if":{"required":["type"],"properties":{"type":{"const":"text"}}},"then":{"$ref":"#/definitions/text"}},{"if":{"required":["type"],"properties":{"type":{"const":"text_alignment"}}},"then":{"$ref":"#/definitions/text_alignment"}},{"if":{"required":["type"],"properties":{"type":{"const":"textarea"}}},"then":{"$ref":"#/definitions/textarea"}},{"if":{"required":["type"],"properties":{"type":{"const":"url"}}},"then":{"$ref":"#/definitions/url"}},{"if":{"required":["type"],"properties":{"type":{"const":"video"}}},"then":{"$ref":"#/definitions/video"}},{"if":{"required":["type"],"properties":{"type":{"const":"video_url"}}},"then":{"$ref":"#/definitions/video_url"}}],"definitions":{"article":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"article","description":"A setting of type article outputs an article picker field that's automatically populated with the available articles for the store. You can use these fields to capture an article selection, such as the article to feature on the homepage.","markdownDescription":"A setting of type `article` outputs an article picker field that's automatically populated with the available articles for the store. You can use these fields to capture an article selection, such as the article to feature on the homepage.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#article)"},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"blog":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"blog","description":"A setting of type blog outputs a blog picker field that's automatically populated with the available blogs for the store. You can use these fields to capture a blog selection, such as the blog to feature in the sidebar.","markdownDescription":"A setting of type `blog` outputs a blog picker field that's automatically populated with the available blogs for the store. You can use these fields to capture a blog selection, such as the blog to feature in the sidebar.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#blog)"},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"checkbox":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"checkbox","description":"A setting of type checkbox outputs a checkbox field. These fields can be used for toggling features on and off, such as whether to show an announcement bar.","markdownDescription":"A setting of type `checkbox` outputs a checkbox field. These fields can be used for toggling features on and off, such as whether to show an announcement bar.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#checkbox)"},"default":{"type":"boolean"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"collection":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"collection","description":"A setting of type collection outputs a collection picker field that's automatically populated with the available collections for the store. You can use these fields to capture a collection selection, such as a collection for featuring products on the homepage.","markdownDescription":"A setting of type `collection` outputs a collection picker field that's automatically populated with the available collections for the store. You can use these fields to capture a collection selection, such as a collection for featuring products on the homepage.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#collection)"},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"collection_list":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"collection_list","description":"A setting of type collection_list outputs a collection picker field that's automatically populated with the available collections for the store. You can use these fields to capture multiple collections, such as a group of collections to feature on the homepage.","markdownDescription":"A setting of type `collection_list` outputs a collection picker field that's automatically populated with the available collections for the store. You can use these fields to capture multiple collections, such as a group of collections to feature on the homepage.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#collection_list)"},"limit":{"type":"integer","description":"The maximum number that the merchant can select. The default limit, and the maximum limit you can set, is 50."},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"color":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"color","description":"A setting of type color outputs a color picker field. You can use these fields to capture a color selection for various theme elements, such as the body text color.","markdownDescription":"A setting of type `color` outputs a color picker field. You can use these fields to capture a color selection for various theme elements, such as the body text color.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#color)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true,"alpha":{"type":"boolean","description":"A boolean value that determines whether the color picker should include an alpha slider. The default value is true.","markdownDescription":"A boolean value that determines whether the color picker should include an alpha slider. The default value is true.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#color)"}},"additionalProperties":false},"color_background":{"type":"object","allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"color_background","description":"A setting of type color_background outputs a text field for entering CSS background properties. You can use these fields to capture background settings for various theme elements, such as the store background.","markdownDescription":"A setting of type `color_background` outputs a text field for entering [CSS background](https://developer.mozilla.org/en-US/docs/Web/CSS/background) properties. You can use these fields to capture background settings for various theme elements, such as the store background.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#color_background)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"color_scheme":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"color_scheme","description":"A setting of type color_scheme outputs a picker with all of the available theme color schemes, and a preview of the selected color scheme.","markdownDescription":"A setting of type `color_scheme` outputs a picker with all of the available theme color schemes, and a preview of the selected color scheme.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#color_scheme)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"color_scheme_group":{"allOf":[{"$ref":"#/definitions/colorSchemeGroupStandardAttributes"}],"properties":{"type":{"const":"color_scheme_group","description":"A setting of type color_scheme_group outputs a color scheme.","markdownDescription":"A setting of type `color_scheme_group` outputs a color scheme which is composed of the following input setting types:\n\n- `header`\n- `color`\n- `color_background`\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#color_scheme_group)"},"definition":{"description":"An array of header, color and color_background input settings.","markdownDescription":"An array of `header`, `color` and `color_background` input settings.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#color_scheme_group)","type":"array","items":{"type":"object","properties":{"type":{"enum":["header","color","color_background"]},"visible_if":{"not":{"type":"string"},"errorMessage":"The property visible_if is not allowed within color_scheme_group"}},"allOf":[{"if":{"required":["type"],"properties":{"type":{"const":"header"}}},"then":{"$ref":"#/definitions/header"}},{"if":{"required":["type"],"properties":{"type":{"const":"color"}}},"then":{"$ref":"#/definitions/color"}},{"if":{"required":["type"],"properties":{"type":{"const":"color_background"}}},"then":{"$ref":"#/definitions/color_background"}}]}},"role":{"type":"object","properties":{"background":{"description":"Renders the background color of the preview","oneOf":[{"type":"string"},{"$ref":"#/definitions/gradient"}]},"text":{"description":"Renders the text color of the preview","type":"string"},"primary_button":{"description":"Renders the first pill in the preview","oneOf":[{"type":"string"},{"$ref":"#/definitions/gradient"}]},"secondary_button":{"description":"Renders the second pill in the preview","oneOf":[{"type":"string"},{"$ref":"#/definitions/gradient"}]},"primary_button_border":{"description":"Render the first pills' border in the preview","type":"string"},"secondary_button_border":{"description":"Render the second pills' border in the preview","type":"string"},"on_primary_button":{"description":"Not used in the preview","type":"string"},"on_secondary_button":{"description":"Not used in the preview","type":"string"},"links":{"description":"Not used in the preview","type":"string"},"icons":{"description":"Not used in the preview","type":"string"}},"required":["background","text","primary_button","secondary_button","primary_button_border","secondary_button_border","on_primary_button","on_secondary_button","links","icons"]},"info":true,"id":true},"required":["definition","role"],"additionalProperties":false},"font_picker":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"font_picker","description":"A setting of type font_picker outputs a font picker field that's automatically populated with fonts from the Shopify font library. This library includes web-safe fonts, a selection of Google Fonts, and fonts licensed by Monotype.","markdownDescription":"A setting of type `font_picker` outputs a font picker field that's automatically populated with fonts from the [Shopify font library](https://shopify.dev/docs/themes/architecture/settings/fonts#shopify-font-library). This library includes web-safe fonts, a selection of Google Fonts, and fonts licensed by Monotype.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#font_picker)"},"default":{"type":"string","description":"The default font from the Shopify font library.","markdownDescription":"The default font from the [Shopify font library](https://shopify.dev/docs/themes/architecture/settings/fonts#shopify-font-library)."},"label":true,"info":true,"id":true,"visible_if":true},"required":["default"],"additionalProperties":false},"html":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"html","description":"A setting of type html outputs a multi-line text field that accepts HTML markup.","markdownDescription":"A setting of type `html` outputs a multi-line text field that accepts HTML markup.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#html)"},"default":{"type":"string"},"placeholder":{"type":"string","description":"A placeholder value for the input."},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"image_picker":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"image_picker","description":"A setting of type image_picker outputs an image picker field that's automatically populated with the available images from the Files section of Shopify admin, and has the option to upload new images. Merchants also have an opportunity to enter alt text and select a focal point for their image.","markdownDescription":"A setting of type `image_picker` outputs an image picker field that's automatically populated with the available images from the [Files](https://help.shopify.com/manual/shopify-admin/productivity-tools/file-uploads) section of Shopify admin, and has the option to upload new images. Merchants also have an opportunity to enter alt text and select a [focal point](https://shopify.dev/docs/themes/architecture/settings/input-settings#image-focal-points) for their image.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#image_picker)"},"default":true,"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"inline_richtext":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"inline_richtext","description":"A setting of type inline_richtext outputs HTML markup that isn't wrapped in paragraph tags (<p>)","markdownDescription":"A setting of type `inline_richtext` outputs HTML markup that isn't wrapped in paragraph tags (`<p>`). The setting includes the following basic formatting options:\n\n- Bold\n- Italic\n- Link\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#inline_richtext)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"link_list":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"link_list","description":"A setting of type link_list outputs a menu picker field that's automatically populated with the available menus for the store. You can use these fields to capture a menu selection, such as the menu to use for footer links.","markdownDescription":"A setting of type `link_list` outputs a menu picker field that's automatically populated with the available menus for the store. You can use these fields to capture a menu selection, such as the menu to use for footer links.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#link_list)"},"default":true,"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"liquid":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"liquid","description":"A setting of type liquid outputs a multi-line text field that accepts HTML and limited Liquid markup. You can use these fields to capture custom blocks of HTML and Liquid content, such as a product-specific message. Merchants can also use a liquid setting to add the code needed to integrate certain types of apps into your theme.","markdownDescription":"A setting of type `liquid` outputs a multi-line text field that accepts HTML and [limited](https://shopify.dev/docs/themes/architecture/settings/input-settings#limitations) Liquid markup. You can use these fields to capture custom blocks of HTML and Liquid content, such as a product-specific message. Merchants can also use a liquid setting to add the code needed to integrate certain types of [apps](https://shopify.dev/docs/apps/online-store) into your theme.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#liquid)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"metaobject":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"metaobject","description":"A setting of type metaobject outputs a metaobject picker field that's automatically populated with the compatible metaobject entries for the store. You can use these fields to capture a metaobject entry selection for a known metaobject type.","markdownDescription":"A setting of type `metaobject` outputs a metaobject picker field that's automatically populated with the compatible metaobject entries for the store. You can use these fields to capture a metaobject entry selection for a known metaobject type. \n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#metaobject)"},"metaobject_type":{"type":"string","description":"The metaobject type allowed by the picker."},"default":true,"label":true,"info":true,"id":true},"required":["metaobject_type"],"additionalProperties":false},"metaobject_list":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"metaobject_list","description":"A setting of type metaobject_list outputs a metaobject picker field that's automatically populated with the compatible metaobject entries for the store. You can use these fields to capture multiple metaobject entry selections for a known metaobject type.","markdownDescription":"A setting of type `metaobject_list` outputs a metaobject picker field that's automatically populated with the compatible metaobject entries for the store. You can use these fields to capture multiple metaobject entry selections for a known metaobject type.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#metaobject_list)"},"metaobject_type":{"type":"string","description":"The metaobject type allowed by the picker."},"limit":{"type":"integer","description":"The maximum number that the merchant can select. The default limit, and the maximum limit you can set, is 50."},"default":true,"label":true,"info":true,"id":true},"required":["metaobject_type"],"additionalProperties":false},"number":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"number","description":"A setting of type number outputs a single number field.","markdownDescription":"A setting of type `number` outputs a single number field.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#number)"},"placeholder":{"type":"string","description":"A placeholder value for the input."},"default":{"type":"number"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"page":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"page","description":"A setting of type page outputs a page picker field that's automatically populated with the available pages for the store. You can use these fields to capture a page selection, such as the page to feature content for in a size-chart display.","markdownDescription":"A setting of type `page` outputs a page picker field that's automatically populated with the available pages for the store. You can use these fields to capture a page selection, such as the page to feature content for in a size-chart display.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#page)"},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"product":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"product","description":"A setting of type product outputs a product picker field that's automatically populated with the available products for the store. You can use these fields to capture a product selection, such as the product to feature on the homepage.","markdownDescription":"A setting of type `product` outputs a product picker field that's automatically populated with the available products for the store. You can use these fields to capture a product selection, such as the product to feature on the homepage.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#product)"},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"product_list":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"}],"properties":{"type":{"const":"product_list","description":"A setting of type product_list outputs a product picker field that's automatically populated with the available products for the store. You can use these fields to capture multiple products, such as a group of products to feature on the homepage.","markdownDescription":"A setting of type `product_list` outputs a product picker field that's automatically populated with the available products for the store. You can use these fields to capture multiple products, such as a group of products to feature on the homepage.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#product_list)"},"limit":{"type":"integer","description":"The maximum number that the merchant can select. The default limit, and the maximum limit you can set, is 50."},"default":true,"label":true,"info":true,"id":true},"additionalProperties":false},"radio":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"radio","description":"A setting of type radio outputs a radio option field.","markdownDescription":"A setting of type `radio` outputs a radio option field.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#radio)"},"default":{"type":"string","description":"The value of the default option"},"options":{"$ref":"#/definitions/options"},"label":true,"info":true,"id":true,"visible_if":true},"required":["options"],"additionalProperties":false},"range":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"range","description":"A setting of type range outputs a range slider field with an input field.","markdownDescription":"A setting of type `range` outputs a range slider field with an input field.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#range)"},"default":{"type":"number"},"min":{"type":"number","description":"The minimum value of the input"},"max":{"type":"number","description":"The maximum value of the input"},"step":{"type":"number","multipleOf":0.1,"description":"The increment size between steps of the slider"},"unit":{"type":"string","description":"The unit for the input. For example, you can set \"px\" for a font-size slider","markdownDescription":"The unit for the input. For example, you can set `px` for a font-size slider"},"label":true,"info":true,"id":true,"visible_if":true},"required":["default","min","max"],"additionalProperties":false},"richtext":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"richtext","description":"A setting of type richtext outputs a multi-line text field.","markdownDescription":"A setting of type `richtext` outputs a multi-line text field with the following basic formatting options:\n\n- Bold\n- Italic\n- Underline\n- Link\n- Paragraph\n- Unordered list\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#richtext)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"select":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"select","description":"A setting of type select outputs different selector fields, depending on certain criteria.","markdownDescription":"A setting of type `select` outputs [different selector fields](https://shopify.dev/docs/themes/architecture/settings/input-settings#selector-fields), depending on certain criteria.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#select)"},"default":{"type":"string","description":"The value of the default option"},"group":{"type":"string","description":"An optional attribute that you can add to each option to create option groups in the drop-down."},"options":{"$ref":"#/definitions/options"},"label":true,"info":true,"id":true,"visible_if":true},"required":["options"],"additionalProperties":false},"lengthPattern":{"type":"string","pattern":"^[0-9]+(px|%)$"},"lengthAutoPattern":{"type":"string","pattern":"^([0-9]+(px|%)|auto|fit-content)$"},"lengthNonePattern":{"type":"string","pattern":"^([0-9]+(px|%)|none|fit-content)$"},"negativeLengthPattern":{"type":"string","pattern":"^-?[0-9]+(px|%)$"},"numberPattern":{"type":"string","pattern":"^[0-9]+$"},"negativeNumberPattern":{"type":"string","pattern":"^-?[0-9]+$"},"style.layout_panel":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"style.layout_panel","description":"A setting of type style.layout_panel outputs style settings for layout.","markdownDescription":"A setting of type `style.layout_panel` outputs style settings for layout.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#style.layout_panel)"},"default":{"allOf":[{"$ref":"#/definitions/style.flex_layout_properties"}],"properties":{"@media (--mobile)":{"type":"object","description":"Style settings for the mobile breakpoint","$ref":"#/definitions/style.flex_layout_properties","unevaluatedProperties":false}},"unevaluatedProperties":false},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"style.size_panel":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"style.size_panel","description":"A setting of type style.size_panel outputs style settings for size.","markdownDescription":"A setting of type `style.size_panel` outputs style settings for size.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#style.size_panel)"},"default":{"allOf":[{"$ref":"#/definitions/style.size_properties"}],"properties":{"@media (--mobile)":{"type":"object","description":"Style settings for the mobile breakpoint","$ref":"#/definitions/style.size_properties","unevaluatedProperties":false}},"unevaluatedProperties":false},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"style.spacing_panel":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"style.spacing_panel","description":"A setting of type style.spacing_panel outputs style settings for spacing.","markdownDescription":"A setting of type `style.spacing_panel` outputs style settings for spacing.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#style.spacing_panel)"},"default":{"allOf":[{"$ref":"#/definitions/style.spacing_properties"}],"properties":{"@media (--mobile)":{"type":"object","description":"Style settings for the mobile breakpoint","$ref":"#/definitions/style.spacing_properties","unevaluatedProperties":false}},"unevaluatedProperties":false},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"text":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"text","description":"A setting of type text outputs a single-line text field.","markdownDescription":"A setting of type `text` outputs a single-line text field.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#text)"},"default":{"type":"string"},"placeholder":{"type":"string","description":"A placeholder value for the input."},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"text_alignment":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"text_alignment","description":"A setting of type text_alignment outputs a SegmentedControl field with icons.","markdownDescription":"A setting of type `text_alignment` outputs a `SegmentedControl` field with icons.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#text_alignment)"},"default":{"type":"string","enum":["left","right","center"]},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"textarea":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"textarea","description":"A setting of type textarea outputs a multi-line text field.","markdownDescription":"A setting of type `textarea` outputs a multi-line text field\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#textarea)"},"default":{"type":"string"},"placeholder":{"type":"string","description":"A placeholder value for the input."},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"url":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"url","description":"A setting of type url outputs a URL entry field where you can manually enter external URLs and relative paths.","markdownDescription":"A setting of type `url` outputs a URL entry field where you can manually enter external URLs and relative paths.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#url)"},"default":{"type":"string"},"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"video":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"video","description":"A setting of type video outputs a video picker that's automatically populated with the available videos from the Files section of the Shopify admin. The merchant also has the option to upload new videos.","markdownDescription":"A setting of type `video` outputs a video picker that's automatically populated with the available videos from the [Files](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/file-uploads) section of the Shopify admin. The merchant also has the option to upload new videos.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#video)"},"default":true,"label":true,"info":true,"id":true,"visible_if":true},"additionalProperties":false},"video_url":{"allOf":[{"$ref":"#/definitions/inputSettingsStandardAttributes"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"video_url","description":"A setting of type video_url outputs a URL entry field.","markdownDescription":"A setting of type `video_url` outputs a URL entry field.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#video_url)"},"placeholder":{"type":"string","description":"A placeholder value for the input."},"accept":{"description":"Takes an array of accepted video providers. Valid values are youtube, vimeo, or both.","markdownDescription":"Takes an array of accepted video providers. Valid values are `youtube`, `vimeo`, or both.","type":"array","items":{"type":"string","enum":["youtube","vimeo"]}},"default":true,"label":true,"info":true,"id":true,"visible_if":true},"required":["accept"],"additionalProperties":false},"header":{"allOf":[{"$ref":"#/definitions/sidebarStandardSettings"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"header","description":"A setting of type `header` outputs a header element to help you better organize your input settings.","markdownDescription":"A setting of type `header` outputs a header element to help you better organize your input settings.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/sidebar-settings#header)"},"info":{"$ref":"#/definitions/info"},"content":true,"visible_if":true},"additionalProperties":false},"paragraph":{"allOf":[{"$ref":"#/definitions/sidebarStandardSettings"},{"$ref":"#/definitions/conditionalSetting"}],"properties":{"type":{"const":"paragraph","description":"A setting of type paragraph outputs a text element to help you better describe your input settings.","markdownDescription":"A setting of type `paragraph` outputs a text element to help you better describe your input settings.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/sidebar-settings#paragraph)"},"content":true,"visible_if":true},"additionalProperties":false},"sidebarStandardSettings":{"$comment":"Sidebar standard settings","required":["type","content"],"properties":{"content":{"type":"string","description":"The setting content, which will show in the theme editor."}}},"conditionalSetting":{"$comment":"Conditional setting property","properties":{"visible_if":{"type":"string","description":"A liquid expression that determines whether the setting should be visible.","markdownDescription":"A liquid expression that determines whether the setting should be visible.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#visible_if)"}}},"inputSettingsStandardAttributes":{"required":["type","id","label"],"properties":{"id":{"type":"string","description":"The unique identifier for the setting, which is used to access the setting value.","markdownDescription":"The unique identifier for the setting, which is used to access the setting value.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#standard-attributes)"},"label":{"type":"string","description":"The label for the setting, which will show in the theme editor."},"default":{"description":"The default value for the setting."},"info":{"$ref":"#/definitions/info"}}},"colorSchemeGroupStandardAttributes":{"required":["type","id"],"properties":{"id":{"type":"string","description":"The unique identifier for the setting, which is used to access the setting value.","markdownDescription":"The unique identifier for the setting, which is used to access the setting value.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings/input-settings#standard-attributes)"}}},"info":{"type":"string","description":"An option for informational text about the setting."},"gradient":{"type":"object","required":["solid","gradient"],"properties":{"solid":{"type":"string"},"gradient":{"type":"string"}}},"style.flex_layout_properties":{"type":"object","properties":{"flex-direction":{"type":"string","description":"Determines how flex items are arranged within the flex container by specifying the main axis and direction. Valid values are row (default), row-reverse, column, and column-reverse.","enum":["row","row-reverse","column","column-reverse"]},"flex-wrap":{"type":"string","description":"Specifies whether flex items are confined to a single line or can flow onto multiple lines. If wrapping is allowed, it defines the stacking direction of the lines. Valid values are nowrap (default), wrap, and wrap-reverse.","enum":["nowrap","wrap","wrap-reverse"]},"row-gap":{"$ref":"#/definitions/lengthPattern","description":"Defines the size of the gap between the rows of a wrapped flex container."},"column-gap":{"$ref":"#/definitions/lengthPattern","description":"Defines the size of the gap between flex items."},"gap":{"type":"string","description":"Shorthand for row-gap and column-gap. Defines the gaps between flex items.","pattern":"^([0-9]+(px|%) ?){1,2}$"},"justify-content":{"type":"string","description":"Defines the alignment of flex items along the main axis of the flex container. Valid values are flex-start (default), flex-end, start, end, left, right, center, space-between, space-around and space-evenly.","enum":["flex-start","flex-end","start","end","left","right","center","space-between","space-around","space-evenly"]},"align-items":{"type":"string","description":"Defines the alignment of flex items along the cross axis of the flex container. Valid values are stretch (default), flex-start / start / self-start, flex-end / end / self-end, center and baseline.","enum":["stretch","flex-start","start","self-start","flex-end","end","self-end","center","baseline"]},"align-content":{"type":"string","description":"Defines the distribution of space between and around flex items along the cross axis. Valid values are stretch, flex-start / start, flex-end / end, center, space-between, space-around, and space-evenly.","enum":["stretch","flex-start","start","flex-end","end","center","space-between","space-around","space-evenly"]}}},"style.size_properties":{"type":"object","properties":{"flex-grow":{"$ref":"#/definitions/numberPattern","description":"Defines the flex grow factor of a flex item, determining how much of the available space in the flex container the item should occupy."},"flex-shrink":{"$ref":"#/definitions/numberPattern","description":"Defines the flex shrink factor of a flex item, determining how much the item should reduce in size compared to the other flex items when space is insufficient."},"flex-basis":{"$ref":"#/definitions/lengthAutoPattern","description":"Defines the initial main size of a flex item, determining the size of the content-box unless otherwise specified by box-sizing."},"width":{"$ref":"#/definitions/lengthAutoPattern","description":"Defines the width of an item."},"min-width":{"$ref":"#/definitions/lengthAutoPattern","description":"Defines the minimum width of an item."},"max-width":{"$ref":"#/definitions/lengthNonePattern","description":"Defines the maximum width of an item."},"height":{"$ref":"#/definitions/lengthAutoPattern","description":"Defines the height of an item."},"min-height":{"$ref":"#/definitions/lengthAutoPattern","description":"Defines the minimum height of an item."},"max-height":{"$ref":"#/definitions/lengthNonePattern","description":"Defines the maximum height of an item."}}},"style.spacing_properties":{"type":"object","properties":{"padding":{"description":"Shorthand that defines the padding on all four sides of an element.","pattern":"^([0-9]+(px|%) ?){1,4}$"},"padding-top":{"description":"Defines the padding on the top side of an element.","$ref":"#/definitions/lengthPattern"},"padding-right":{"description":"Defines the padding on the right side of an element.","$ref":"#/definitions/lengthPattern"},"padding-bottom":{"description":"Defines the padding on the bottom side of an element.","$ref":"#/definitions/lengthPattern"},"padding-left":{"description":"Defines the padding on the left side of an element.","$ref":"#/definitions/lengthPattern"},"padding-block-start":{"description":"Defines the logical block start padding of an element, translating to physical padding based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/lengthPattern"},"padding-block-end":{"description":"Defines the logical block end padding of an element, translating to physical padding based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/lengthPattern"},"padding-block":{"type":"string","description":"Shorthand for padding-block-start and padding-block end. Defines the logical block start and end padding of an element, translating to physical padding based on the element's writing mode, text direction, and text orientation.","pattern":"^([0-9]+(px|%) ?){1,2}$"},"padding-inline-start":{"description":"Defines the logical inline start padding of an element, translating to physical padding based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/lengthPattern"},"padding-inline-end":{"description":"Defines the logical inline end padding of an element, translating to physical padding based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/lengthPattern"},"padding-inline":{"type":"string","description":"Shorthand that defines the logical inline start and end padding of an element, translating to physical padding based on the element's writing mode, text direction, and text orientation.","pattern":"^([0-9]+(px|%) ?){1,2}$"},"margin":{"type":"string","description":"Shorthand that defines the margin on all four sides of an element.","pattern":"^(-?[0-9]+(px|%) ?){1,4}$"},"margin-top":{"description":"Defines the margin on the top side of an element.","$ref":"#/definitions/negativeLengthPattern"},"margin-right":{"description":"Defines the margin on the right side of an element.","$ref":"#/definitions/negativeLengthPattern"},"margin-bottom":{"description":"Defines the margin on the bottom side of an element.","$ref":"#/definitions/negativeLengthPattern"},"margin-left":{"description":"Defines the margin on the left side of an element.","$ref":"#/definitions/negativeLengthPattern"},"margin-block-start":{"description":"Defines the logical block start margin of an element, translating to physical margin based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/negativeLengthPattern"},"margin-block-end":{"description":"Defines the logical block end margin of an element, translating to physical margin based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/negativeLengthPattern"},"margin-block":{"type":"string","description":"Shorthand that defines the logical block start and end margins of an element, translating to physical margin based on the element's writing mode, text direction, and text orientation.","pattern":"^(-?[0-9]+(px|%) ?){1,2}$"},"margin-inline-start":{"description":"Defines the logical inline start margin of an element, translating to physical margin based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/negativeLengthPattern"},"margin-inline-end":{"description":"Defines the logical inline end margin of an element, translating to physical margin based on the element's writing mode, text direction, and text orientation.","$ref":"#/definitions/negativeLengthPattern"},"margin-inline":{"type":"string","description":"Shorthand that defines both the logical inline start and end margins of an element, translating to physical margin based on the element's writing mode, text direction, and text orientation.","pattern":"^(-?[0-9]+(px|%) ?){1,2}$"}}},"options":{"description":"Takes an array of `value`/`label` definitions.","type":"array","items":{"type":"object","properties":{"value":{"description":"The value of the option.","type":"string"},"label":{"description":"The label of the option.","type":"string"}},"required":["value","label"]}}}}
```

**schemas/settings.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","description":"Settings that merchants can configure through the theme editor.","markdownDescription":"Settings that merchants can configure through the [theme editor](https://help.shopify.com/en/manual/online-store/themes/customizing-themes#theme-editor)\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/settings).","type":"array","items":{"$ref":"./setting.json"},"minItems":0}
```

**schemas/targetted_block_entry.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","$comment":"A targetted private or public theme block from the blocks/ folder.","type":"object","required":["type"],"additionalProperties":false,"properties":{"type":{"type":"string","pattern":"^[a-zA-Z0-9_-]+$","description":"The name of a theme block found in the blocks/ folder of the theme.","markdownDescription":"The name of a theme block found in the `blocks/` folder of the theme.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#blocks)"}}}
```

**schemas/theme_block_entry.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","$comment":"A @theme block entry","type":"object","additionalProperties":false,"required":["type"],"properties":{"type":{"const":"@theme","description":"The \"@theme\" type denotes that this container accepts theme blocks that live in the blocks/ folder of the theme.","markdownDescription":"The `@theme` type denotes that this container accepts theme blocks that live in the `blocks/` folder of the theme.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#blocks)"}}}
```

**schemas/theme_block.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","title":"Shopify Liquid Theme Block Schema","type":"object","additionalProperties":false,"properties":{"name":{"type":"string","description":"The name attribute determines the block title that's shown in the theme editor.","markdownDescription":"The `name` attribute determines the block title that's shown in the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#name)"},"settings":{"$ref":"./settings.json"},"blocks":{"type":"array","description":"Theme blocks can accept other app and theme blocks as children using the blocks attribute of their schema.","markdownDescription":"Theme blocks can accept other app and theme blocks as children using the `blocks` attribute of their schema.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#blocks)","items":{"type":"object","additionalProperties":false,"required":["type"],"properties":{"type":{"type":"string","description":"The type of block that can be added to this block.","markdownDescription":"The type of block that can be added to this block.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#blocks)"}},"$comment":"The allOf rule here exists because that's how we do discriminated unions in JSON schemas. If a rule matches, that rule will be used to document the type property. Otherwise we fallback to the docs above.","allOf":[{"if":{"required":["type"],"properties":{"type":{"const":"@theme"}}},"then":{"$ref":"./theme_block_entry.json"}},{"if":{"required":["type"],"properties":{"type":{"const":"@app"}}},"then":{"$ref":"./app_block_entry.json"}},{"if":{"required":["type"],"properties":{"type":{"type":"string","not":{"enum":["@app","@theme"]}}}},"then":{"$ref":"./targetted_block_entry.json"}}]}},"presets":{"type":"array","description":"Presets are default configurations of blocks that enable merchants to easily add a block to a JSON template through the theme editor.","markdownDescription":"Presets are default configurations of blocks that enable merchants to easily add a block to a JSON template through the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#presets)","items":{"$ref":"./preset.json"}},"tag":{"description":"The HTML element that is used to wrap the rendered block. Accepts any string up to 50 characters. Can be used to render custom HTML elements. Use null to render without a wrapping element.","markdownDescription":"The HTML element that is used to wrap the rendered block. Accepts any string up to 50 characters. Can be used to render custom HTML elements.\n\nUse `null` to render without a wrapping element.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#tag)","oneOf":[{"description":"If you don't want to use a <div>, then you can specify which kind of HTML element to use.","markdownDescription":"If you don't want to use a `<div>`, then you can specify which kind of HTML element to use.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#tag)","type":"string","maxLength":50},{"description":"Used to render the block without a wrapping element.","markdownDescription":"Used to render the block without a wrapping element.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#rendering-blocks-without-a-wrapper)","type":"null"}]},"class":{"type":"string","description":"When Shopify renders a block, it's wrapped in an HTML element with the shopify-block class. You can append other classes by using the class attribute.","markdownDescription":"When Shopify renders a block, it's wrapped in an HTML element with the `shopify-block` class. You can append other classes by using the class attribute.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/blocks/theme-blocks/schema#class)"}}}
```

**schemas/theme_settings.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","title":"JSON schema for config/settings_schema.json files.","description":"The settings that merchants can configure in the theme editor.","markdownDescription":"The settings that merchants can configure in the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/config/settings-schema-json)","type":"array","items":{"anyOf":[{"title":"Theme metadata","markdownDescription":"Additional metadata for your theme that shows up in the Theme actions menu of the theme editor.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/config/settings-schema-json#add-theme-metadata)","type":"object","properties":{"name":{"description":"You may use the 'theme_info' object for theme metadata.","markdownDescription":"[Shopify reference](https://shopify.dev/docs/themes/architecture/config/settings-schema-json#add-theme-metadata)","const":"theme_info"},"theme_name":{"type":"string","description":"The name of the theme."},"theme_author":{"type":"string","description":"The author of the theme."},"theme_version":{"type":"string","description":"The version number of the theme."},"theme_documentation_url":{"type":"string","format":"uri","description":"A URL where merchants can find documentation for the theme."},"theme_support_email":{"type":"string","format":"email","description":"An email address that merchants can contact for support for the theme."},"theme_support_url":{"type":"string","format":"uri","description":"A URL where merchants can find support for the theme."}},"required":["name","theme_name","theme_author","theme_version","theme_documentation_url"],"oneOf":[{"required":["theme_support_email"],"not":{"required":["theme_support_url"]}},{"required":["theme_support_url"],"not":{"required":["theme_support_email"]}}]},{"type":"object","properties":{"name":{"type":"string","description":"The name of the category of settings.","markdownDescription":"The name of the category of settings.\n\n---\n\n[Shopify reference](https://shopify.dev/docs/themes/architecture/config/settings-schema-json#schema)","not":{"const":"theme_info"}},"settings":{"$ref":"./settings.json"}}}]}}
```

**schemas/translations.json**
```
{"$schema":"http://json-schema.org/draft-07/schema#","type":"object","additionalProperties":{"anyOf":[{"type":"string"},{"$ref":"#/definitions/pluralizedString"},{"$ref":"#"}]},"patternProperties":{".*_html$":{"oneOf":[{"type":"string","description":"A string that can contain HTML content, typically used for translations that include HTML tags."},{"$ref":"#/definitions/pluralizedString"}]}},"definitions":{"pluralizedString":{"type":"object","properties":{"one":{"type":"string","description":"Translation for the singular form"},"other":{"type":"string","description":"Translation for the plural form"},"few":{"type":"string","description":"Translation for 'few' form, used in some languages"},"many":{"type":"string","description":"Translation for 'many' form, used in some languages"},"two":{"type":"string","description":"Translation for 'two' form, used in some languages"},"zero":{"type":"string","description":"Translation for 'zero' form, used in some languages"}},"additionalProperties":false,"description":"An object representing a pluralized translation string"}}}
```


